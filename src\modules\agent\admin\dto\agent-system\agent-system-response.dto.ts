import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { SystemModelDto } from './system-model.dto';
import { McpSystemDto } from './mcp-system.dto';

/**
 * DTO cho thông tin vector store
 */
export class VectorStoreDto {
  /**
   * ID của vector store
   */
  @ApiProperty({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  vectorStoreId: string;

  /**
   * Tên của vector store
   */
  @ApiProperty({
    description: 'Tên của vector store',
    example: 'Vector Store 1',
  })
  vectorStoreName: string;
}

/**
 * DTO cho thông tin vai trò đầy đủ
 */
export class RoleDto {
  /**
   * ID của vai trò
   */
  @ApiProperty({
    description: 'ID của vai trò',
    example: 'role-id-1',
  })
  id: string;

  /**
   * Tên của vai trò
   */
  @ApiProperty({
    description: 'Tên của vai trò',
    example: 'Admin Assistant',
  })
  name: string;

  /**
   * Mô tả của vai trò
   */
  @ApiPropertyOptional({
    description: 'Mô tả của vai trò',
    example: 'Vai trò hỗ trợ quản trị viên',
  })
  description: string | null;

  /**
   * Số lượng quyền trong vai trò
   */
  @ApiPropertyOptional({
    description: 'Số lượng quyền trong vai trò',
    example: 5,
  })
  permissionCount?: number;
}

/**
 * DTO cho thông tin vai trò đơn giản (chỉ id và name)
 */
export class SimpleRoleDto {
  /**
   * ID của vai trò
   */
  @ApiProperty({
    description: 'ID của vai trò',
    example: 'role-id-1',
  })
  id: string;

  /**
   * Tên của vai trò
   */
  @ApiProperty({
    description: 'Tên của vai trò',
    example: 'Admin Assistant',
  })
  name: string;
}

/**
 * DTO cho thông tin model config - chỉ bao gồm các trường cần thiết
 */
export class ModelConfigResponseDto {
  /**
   * Giá trị temperature cho model (0-2)
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model (0-2)',
    example: 1.0,
  })
  temperature?: number;

  /**
   * Giá trị top_p cho model (0-1)
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_p cho model (0-1)',
    example: 1.0,
  })
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_k cho model',
    example: 1.0,
  })
  top_k?: number;

  /**
   * Số token tối đa cho kết quả
   */
  @ApiPropertyOptional({
    description: 'Số token tối đa cho kết quả',
    example: 1000,
  })
  max_tokens?: number;
}

/**
 * DTO cho thông tin agent system trong danh sách
 */
export class AgentSystemListItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * Mã định danh của agent system, dùng để định danh trong code
   */
  @ApiProperty({
    description: 'Mã định danh của agent system, dùng để định danh trong code',
    example: 'system_assistant',
  })
  nameCode: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar: string | null;

  /**
   * Tên model sử dụng
   */
  @ApiProperty({
    description: 'Tên model sử dụng',
    example: 'gpt-4o',
  })
  model: string;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  active: boolean;

  /**
   * Loại provider
   */
  @ApiPropertyOptional({
    description: 'Loại provider',
    example: 'OPENAI',
  })
  provider?: string | null;

  /**
   * Thông tin vai trò (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Thông tin vai trò (nếu có)',
    type: Boolean,
  })
  isSupervisor?: boolean;
}

/**
 * DTO cho thông tin chi tiết agent system
 */
export class AgentSystemDetailDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * Mã định danh của agent system, dùng để định danh trong code
   */
  @ApiProperty({
    description: 'Mã định danh của agent system, dùng để định danh trong code',
    example: 'system_assistant',
  })
  nameCode: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar: string | null;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigResponseDto,
  })
  modelConfig: ModelConfigResponseDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  instruction: string | null;

  /**
   * Mô tả về agent system
   */
  @ApiPropertyOptional({
    description: 'Mô tả về agent system',
    example: 'Mô tả về agent system supervisor',
  })
  description: string | null;

  /**
   * Thông tin vector store
   */
  @ApiPropertyOptional({
    description: 'Thông tin vector store',
    type: VectorStoreDto,
  })
  vector?: VectorStoreDto;

  /**
   * ID của system model được sử dụng
   */
  @ApiProperty({
    description: 'ID của system model được sử dụng',
    example: 'model-uuid-123',
  })
  model: SystemModelDto;

  /**
   * Danh sách MCP systems
   */
  @ApiPropertyOptional({
    description: 'Danh sách MCP systems',
    type: [McpSystemDto],
  })
  mcp?: McpSystemDto[];

  /**
   * Thông tin người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: EmployeeInfoDto,
  })
  created?: EmployeeInfoDto;

  /**
   * Thông tin người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Thông tin người cập nhật',
    type: EmployeeInfoDto,
  })
  updated?: EmployeeInfoDto;

  /**
   * Thông tin người xóa
   */
  @ApiPropertyOptional({
    description: 'Thông tin người xóa',
    type: EmployeeInfoDto,
  })
  deleted?: EmployeeInfoDto;
}

/**
 * DTO cho response danh sách agent system đã xóa
 */
export class AgentSystemTrashItemDto {
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  @ApiProperty({
    description: 'Mã định danh của agent system',
    example: 'system_assistant',
  })
  nameCode: string;

  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar?: string | null;

  @ApiProperty({
    description: 'Tên model sử dụng',
    example: 'gpt-4o',
  })
  model: string;

  @ApiPropertyOptional({
    description: 'Model ID từ provider',
    example: 'gpt-4o',
  })
  model_id?: string | null;

  @ApiPropertyOptional({
    description: 'Loại provider',
    example: 'OPENAI',
  })
  type_provider?: string | null;

  @ApiProperty({
    description: 'Trạng thái của agent',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.APPROVED,
  })
  status: AgentStatusEnum;

  @ApiPropertyOptional({
    description: 'Thông tin người xóa',
    type: EmployeeInfoDto,
  })
  deleted?: EmployeeInfoDto;

  @ApiPropertyOptional({
    description: 'Thông tin vai trò (nếu có)',
    type: SimpleRoleDto,
  })
  roles?: SimpleRoleDto;
}