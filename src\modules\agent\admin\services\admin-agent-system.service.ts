import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType } from '@/shared/utils';
import { AppException, ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { AgentSystemMcpRepository } from '@modules/agent/repositories/agent-system-mcp.repository';
import { McpSystemsRepository } from '@modules/agent/repositories/mcp-systems.repository';
import { SystemModelsRepository } from '@modules/models/repositories/system-models.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { AgentSystemListItemDto, AgentSystemQueryDto, CreateAgentSystemDto, UpdateAgentSystemDto, AgentSystemDetailDto, SystemModelDto, McpSystemDto, AgentSystemTrashItemDto } from '../dto';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { S3Service } from '@/shared/services/s3.service';
import { Agent, AgentSystem } from '../../entities';

/**
 * Service xử lý logic nghiệp vụ cho Agent System
 */
@Injectable()
export class AdminAgentSystemService {
  private readonly logger = new Logger(AdminAgentSystemService.name);

  constructor(
    private readonly agentSystemRepository: AgentSystemRepository,
    private readonly agentRepository: AgentRepository,
    private readonly agentSystemMcpRepository: AgentSystemMcpRepository,
    private readonly mcpSystemsRepository: McpSystemsRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
  ) { }

  /**
   * Lấy danh sách agent system với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system với phân trang
   */
  async findAll(
    queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    try {
      this.logger.log('Getting agent system list with pagination');

      // Lấy danh sách agent system từ repository với thông tin chi tiết
      const result = await this.agentSystemRepository.findPaginatedWithAgentInfo(
        queryDto.page || 1,
        queryDto.limit || 10,
        queryDto.search,
        queryDto.active,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Chuyển đổi kết quả sang DTO
      const items = result.items.map((agentSystemData: any) => {
        return this.mapToListItemDto(agentSystemData);
      });

      const page = queryDto.page || 1;
      const limit = queryDto.limit || 10;

      const paginatedResult = {
        items,
        meta: {
          totalItems: result.total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(result.total / limit),
          currentPage: page,
          hasItems: result.total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent system thành công');
    } catch (error) {
      this.logger.error(`Failed to get agent system list: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy danh sách agent system'
      );
    }
  }

  /**
   * Map raw result từ join query sang AgentSystemListItemDto
   * @param agentSystemData Raw result từ database join
   * @returns AgentSystemListItemDto
   */
  private mapToListItemDto(agentSystemData: any): AgentSystemListItemDto {
    const dto = new AgentSystemListItemDto();
    dto.id = agentSystemData.id;
    dto.name = agentSystemData.name || 'Unknown Agent';
    dto.nameCode = agentSystemData.namecode || '';
    dto.avatar = agentSystemData.avatar ? this.cdnService.generateUrlView(agentSystemData.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.model = agentSystemData.modelid || 'Unknown Model';
    dto.provider = agentSystemData.provider || null;
    dto.active = agentSystemData.active || false;
    dto.isSupervisor = agentSystemData.issupervisor || false;

    return dto;
  }

  /**
   * Lấy thông tin chi tiết agent system theo ID
   * @param id ID của agent system
   * @returns Thông tin chi tiết agent system
   */
  async findById(id: string): Promise<AgentSystemDetailDto> {
    try {
      this.logger.log(`Getting agent system detail with ID: ${id}`);

      // 1. Validate và lấy thông tin cơ bản
      const { agent, agentSystem } = await this.validateAgentSystemExists(id);

      // 2. Lấy thông tin system model
      const systemModel = await this.getSystemModelInfo(agentSystem.systemModelId);

      // 3. Lấy danh sách MCP systems
      const mcpSystems = await this.getMcpSystemsInfo(id);

      // 4. Lấy thông tin vector store (nếu có)
      const vectorStore = await this.getVectorStoreInfo(agent.vectorStoreId);

      // 5. Lấy thông tin employees
      const employeeInfo = await this.getEmployeeInfo(agentSystem);

      // 6. Map sang DTO
      return await this.mapToDetailDto(
        agent,
        agentSystem,
        systemModel,
        mcpSystems,
        vectorStore,
        employeeInfo
      );

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding agent system by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  // /**
  //  * Tạo agent system mới
  //  * @param createDto Dữ liệu tạo agent system
  //  * @param employeeId ID của nhân viên tạo
  //  * @returns URL tải lên avatar
  //  */
  // @Transactional()
  // async create(
  //   createDto: CreateAgentSystemDto,
  //   employeeId: number,
  // ): Promise<{
  //   id: string;
  //   avatarUrlUpload?: string;
  // }> {
  //   // Kiểm tra tên agent đã tồn tại chưa
  //   const existingAgent = await this.agentRepository.findByName(createDto.name);

  //   if (existingAgent) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS);
  //   }

  //   // Kiểm tra mã định danh đã tồn tại chưa
  //   const existingAgentByNameCode = await this.agentSystemRepository.findByNameCode(createDto.nameCode);

  //   if (existingAgentByNameCode) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
  //   }

  //   // Validate model_base_id nếu có
  //   if (createDto.modelBaseId) {
  //     await this.validateBaseModel(createDto.modelBaseId);
  //   }

  //   // Validate model_finetuning_id nếu có
  //   if (createDto.modelFinetuningId) {
  //     await this.validateFineTuningModel(createDto.modelFinetuningId);
  //   }

  //   // Kiểm tra vector store có tồn tại không (nếu có)
  //   if (createDto.vectorStoreId) {
  //     await this.validateVectorStore(createDto.vectorStoreId);
  //   }

  //   // Kiểm tra vai trò có tồn tại không (nếu có)
  //   if (createDto.roleId) {
  //     await this.validateRole(createDto.roleId);
  //   }

  //   try {

  //     // Tạo agent mới
  //     const agent = new Agent();
  //     agent.name = createDto.name;
  //     agent.modelConfig = createDto.modelConfig;
  //     agent.instruction = createDto.instruction;
  //     agent.status = createDto.status || 'DRAFT';

  //     // Tạo agent system
  //     const agentSystem = new AgentSystem();
  //     agentSystem.nameCode = createDto.nameCode;
  //     agentSystem.modelBaseId = createDto.modelBaseId || null;
  //     agentSystem.modelFinetuningId = createDto.modelFinetuningId || null;
  //     agentSystem.createdBy = employeeId;
  //     agentSystem.updatedBy = employeeId;

  //     // Gán roleId nếu có
  //     if (createDto.roleId) {
  //       // Kiểm tra vai trò có tồn tại không
  //       await this.validateRole(createDto.roleId);
  //       agentSystem.roleId = createDto.roleId;
  //     } else {
  //       // Nếu không có roleId, sử dụng một giá trị mặc định hoặc ném lỗi
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND, 'Vai trò là bắt buộc cho agent system');
  //     }

  //     // Tạo URL tải lên avatar (nếu có)
  //     let avatarUrlUpload: { url: string; key: string | null } | undefined;
  //     if (createDto.avatarMimeType) {
  //       // Tạo URL tải lên avatar
  //       avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
  //         this.s3Service,
  //         employeeId.toString(),
  //         createDto.avatarMimeType
  //       );

  //       agent.avatar = avatarUrlUpload.key;
  //     }

  //     // Lưu agent
  //     const savedAgent = await this.agentRepository.save(agent);

  //     // Gán ID của agent cho agentSystem
  //     agentSystem.id = savedAgent.id;

  //     // Lưu agent system
  //     const agentSystemSaved = await this.agentSystemRepository.save(agentSystem);

  //     const url = avatarUrlUpload ? avatarUrlUpload.url : undefined;

  //     return { id: agentSystemSaved.id, avatarUrlUpload: url };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error creating agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
  //   }
  // }

  /**
   * Cập nhật thông tin agent system
   * @param id ID của agent system
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns ID và URL tải lên avatar mới (nếu có)
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentSystemDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Updating agent system with ID: ${id}`);

      // 1. Validate agent system exists
      const { agent, agentSystem } = await this.validateAgentSystemExists(id);

      // 2. Validate nameCode uniqueness (nếu có thay đổi)
      if (updateDto.nameCode && updateDto.nameCode !== agentSystem.nameCode) {
        await this.validateUniqueNameCodeForUpdate(updateDto.nameCode, id);
      }

      // 3. Validate supervisor uniqueness (chỉ validate nếu agent system hiện tại là supervisor)
      if (agentSystem.isSupervisor) {
        await this.validateUniqueSupervisorForUpdate(true, id);
      }

      // 4. Validate system model exists (nếu có modelId trong updateDto)
      if (updateDto.modelId) {
        await this.validateSystemModel(updateDto.modelId);
      }

      // 5. Validate vector store exists (nếu có vectorStoreId trong updateDto)
      if (updateDto.vectorStoreId) {
        await this.validateVectorStore(updateDto.vectorStoreId);
      }

      // 6. Validate MCP systems exist (nếu có mcpId array trong updateDto)
      if (updateDto.mcpId && updateDto.mcpId.length > 0) {
        await this.validateMcpSystems(updateDto.mcpId);
      }

      // 7. Update agent table
      let avatarUrlUpload: string | undefined;

      // Cập nhật thông tin agent
      if (updateDto.name) {
        agent.name = updateDto.name;
      }

      if (updateDto.modelConfig) {
        agent.modelConfig = updateDto.modelConfig;
      }

      if (updateDto.instruction !== undefined) {
        agent.instruction = updateDto.instruction;
      }

      // 7.1. Generate avatar upload URL (nếu có avatarMimeType)
      if (updateDto.avatarMimeType) {

        if (!agent.avatar) {
          agent.avatar = generateS3Key({
            baseFolder: employeeId.toString(),
            categoryFolder: CategoryFolderEnum.AGENT,
          });
        }

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          agent.avatar,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );
      }

      // Lưu thay đổi agent
      await this.agentRepository.save(agent);

      // 8. Update agent system table
      if (updateDto.nameCode) {
        agentSystem.nameCode = updateDto.nameCode;
      }

      if (updateDto.description !== undefined) {
        agentSystem.description = updateDto.description;
      }

      if (updateDto.modelId) {
        agentSystem.systemModelId = updateDto.modelId;
      }

      // Set updatedBy
      agentSystem.updatedBy = employeeId;

      // Lưu thay đổi agent system
      await this.agentSystemRepository.save(agentSystem);

      // 9. Handle MCP systems linking (nếu có mcpId trong updateDto)
      if (updateDto.mcpId !== undefined) {
        if (updateDto.mcpId.length > 0) {
          // Xóa các liên kết MCP cũ và tạo liên kết mới
          await this.agentSystemMcpRepository.unlinkAllMcpsFromAgent(id);
          await this.linkAgentWithMcpSystems(id, updateDto.mcpId);
        } else {
          // Nếu mcpId là array rỗng, xóa tất cả liên kết MCP
          await this.agentSystemMcpRepository.unlinkAllMcpsFromAgent(id);
        }
      }

      this.logger.log(`Successfully updated agent system ${id}`, {
        updatedFields: {
          name: !!updateDto.name,
          nameCode: !!updateDto.nameCode,
          modelConfig: !!updateDto.modelConfig,
          instruction: updateDto.instruction !== undefined,
          description: updateDto.description !== undefined,
          modelId: !!updateDto.modelId,
          vectorStoreId: !!updateDto.vectorStoreId,
          avatarMimeType: !!updateDto.avatarMimeType,
          mcpId: !!updateDto.mcpId
        },
        hasAvatarUpload: !!avatarUrlUpload
      });

      return {
        id,
        avatarUrlUpload
      };

    } catch (error) {
      this.logger.error(`Failed to update agent system ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        `Không thể cập nhật agent system: ${error.message}`
      );
    }
  }

  //   // Kiểm tra tên agent đã tồn tại chưa (nếu có cập nhật tên)
  //   if (updateDto.name && updateDto.name !== agent.name) {
  //     const existingAgent = await this.agentRepository.findByName(
  //       updateDto.name,
  //     );
  //     if (existingAgent && existingAgent.id !== id) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS);
  //     }
  //   }

  //   // Kiểm tra mã định danh đã tồn tại chưa (nếu có cập nhật mã định danh)
  //   if (updateDto.nameCode && updateDto.nameCode !== agentSystem.nameCode) {
  //     const existingAgentByNameCode = await this.agentSystemRepository.findByNameCode(
  //       updateDto.nameCode,
  //     );
  //     if (existingAgentByNameCode && existingAgentByNameCode.id !== id) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
  //     }
  //   }

  //   // Validate model_base_id nếu có cập nhật
  //   if (updateDto.modelBaseId !== undefined && updateDto.modelBaseId !== null) {
  //     await this.validateBaseModel(updateDto.modelBaseId);
  //   }

  //   // Validate model_finetuning_id nếu có cập nhật
  //   if (updateDto.modelFinetuningId !== undefined && updateDto.modelFinetuningId !== null) {
  //     await this.validateFineTuningModel(updateDto.modelFinetuningId);
  //   }

  //   // Kiểm tra vector store có tồn tại không (nếu có cập nhật vector store)
  //   if (updateDto.vectorStoreId) {
  //     await this.validateVectorStore(updateDto.vectorStoreId);
  //   }

  //   // Kiểm tra vai trò có tồn tại không (nếu có cập nhật vai trò)
  //   if (updateDto.roleId) {
  //     await this.validateRole(updateDto.roleId);
  //   }

  //   try {
  //     // Cập nhật thông tin agent
  //     if (updateDto.name) agent.name = updateDto.name;
  //     if (updateDto.modelConfig) agent.modelConfig = updateDto.modelConfig;
  //     if (updateDto.instruction !== undefined)
  //       agent.instruction = updateDto.instruction;
  //     if (updateDto.status) agent.status = updateDto.status;

  //     // Cập nhật thông tin agent system
  //     if (updateDto.nameCode) agentSystem.nameCode = updateDto.nameCode;
  //     if (updateDto.modelBaseId !== undefined) agentSystem.modelBaseId = updateDto.modelBaseId;
  //     if (updateDto.modelFinetuningId !== undefined) agentSystem.modelFinetuningId = updateDto.modelFinetuningId;
  //     agentSystem.updatedBy = employeeId;

  //     // Tạo URL tải lên avatar mới (nếu có)
  //     let avatarUrlUpload: { url: string; key: string | null } | undefined;
  //     if (updateDto.avatarMimeType) {
  //       // Tạo URL tải lên avatar
  //       avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
  //         this.s3Service,
  //         employeeId.toString(),
  //         updateDto.avatarMimeType,
  //         id,
  //         agent.avatar || undefined,
  //       );

  //       if (avatarUrlUpload.key) {
  //         agent.avatar = avatarUrlUpload.key;
  //       }
  //     }

  //     // Lưu agent
  //     await this.agentRepository.save(agent);

  //     // Lưu agent system
  //     await this.agentSystemRepository.save(agentSystem);

  //     // Gán vai trò cho agent system (nếu có)
  //     if (updateDto.roleId) {
  //       await this.assignRoleToAgentSystem(id, updateDto.roleId);
  //     }

  //     const url = avatarUrlUpload ? avatarUrlUpload.url : undefined;

  //     return { avatarUrlUpload: url };
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error updating agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  // /**
  //  * Cập nhật trạng thái agent system
  //  * @param id ID của agent system
  //  * @param updateStatusDto Dữ liệu cập nhật trạng thái
  //  * @param employeeId ID của nhân viên cập nhật
  //  */
  // @Transactional()
  // async updateStatus(
  //   id: string,
  //   updateStatusDto: UpdateAgentSystemStatusDto,
  //   employeeId: number,
  // ): Promise<void> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agent = await this.agentRepository.findById(id);
  //   if (!agent) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //   }

  //   const agentSystem = await this.agentSystemRepository.findById(id);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   try {
  //     // Cập nhật trạng thái agent
  //     agent.status = updateStatusDto.status;

  //     // Lưu agent
  //     await this.agentRepository.save(agent);

  //     // Cập nhật thông tin agent system
  //     agentSystem.updatedBy = employeeId;

  //     // Lưu agent system
  //     await this.agentSystemRepository.save(agentSystem);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error updating agent system status: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(
  //       AGENT_ERROR_CODES.AGENT_SYSTEM_STATUS_UPDATE_FAILED,
  //     );
  //   }
  // }

  /**
   * Xóa agent system (soft delete)
   * @param id ID của agent system
   * @param employeeId ID của nhân viên xóa
   * @returns ID của agent system đã xóa
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<{ id: string }> {
    try {
      this.logger.log(`Removing agent system with ID: ${id}`);

      // Validate agent system exists
      const { agent, agentSystem } = await this.validateAgentSystemExists(id);

      // Cập nhật thông tin xóa cho agent system
      agentSystem.deletedBy = employeeId;
      await this.agentSystemRepository.update({id}, agentSystem);

      // Xóa mềm agent (set deletedAt)
      agent.deletedAt = Date.now();
      await this.agentRepository.update({id}, agent);

      this.logger.log(`Successfully removed agent system ${id}`);

      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing agent system: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Xóa nhiều agent systems (soft delete) - Optimized với bulk query
   * @param ids Danh sách ID của agent systems
   * @param employeeId ID của nhân viên xóa
   * @returns Kết quả xóa với danh sách thành công và thất bại
   */
  @Transactional()
  async removes(ids: string[], employeeId: number): Promise<{ deletedIds: string[]; errorIds: string[] }> {
    try {
      this.logger.log(`Bulk removing agent systems: ${ids.join(', ')}`);

      if (!ids || ids.length === 0) {
        return { deletedIds: [], errorIds: [] };
      }

      // 1. Tìm các agent systems tồn tại và chưa bị xóa
      const existingAgentSystems = await this.agentSystemRepository
        .createQueryBuilder('as')
        .leftJoin('agents', 'a', 'as.id = a.id')
        .select(['as.id', 'a.id'])
        .where('as.id IN (:...ids)', { ids })
        .andWhere('as.deletedBy IS NULL')
        .andWhere('a.deletedAt IS NULL')
        .getRawMany();

      const existingIds = existingAgentSystems.map(item => item.as_id);
      const errorIds = ids.filter(id => !existingIds.includes(id));

      if (existingIds.length === 0) {
        this.logger.warn(`No valid agent systems found for deletion from provided IDs`);
        return { deletedIds: [], errorIds: ids };
      }

      // 2. Bulk update agents_system table - set deletedBy
      await this.agentSystemRepository
        .createQueryBuilder()
        .update()
        .set({ deletedBy: employeeId })
        .where('id IN (:...existingIds)', { existingIds })
        .andWhere('deletedBy IS NULL')
        .execute();

      // 3. Bulk update agents table - set deletedAt
      const currentTimestamp = Date.now();
      await this.agentRepository
        .createQueryBuilder()
        .update()
        .set({ deletedAt: currentTimestamp })
        .where('id IN (:...existingIds)', { existingIds })
        .andWhere('deletedAt IS NULL')
        .execute();

      this.logger.log(`Bulk remove completed. Success: ${existingIds.length}, Failed: ${errorIds.length}`);

      return {
        deletedIds: existingIds,
        errorIds
      };

    } catch (error) {
      this.logger.error(
        `Error in bulk remove agent systems: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  // /**
  //  * Gán vai trò cho agent system
  //  * @param agentId ID của agent system
  //  * @param roleId ID của vai trò
  //  */
  // @Transactional()
  // async assignRoleToAgentSystem(
  //   agentId: string,
  //   roleId: string,
  // ): Promise<void> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agentSystem = await this.agentSystemRepository.findById(agentId);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   // Kiểm tra vai trò có tồn tại không
  //   const role = await this.agentRoleRepository.findById(roleId);
  //   if (!role) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
  //   }

  //   try {
  //     // Gán roleId cho agentSystem
  //     agentSystem.roleId = roleId;

  //     // Lưu agent system với roleId mới
  //     await this.agentSystemRepository.save(agentSystem);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error assigning role to agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  /**
   * Lấy danh sách agent system đã xóa với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system đã xóa với phân trang
   */
  async getDeletedAgentSystems(
    queryDto: AgentSystemQueryDto,
  ): Promise<PaginatedResult<AgentSystemTrashItemDto>> {
    try {
      this.logger.log('Getting deleted agent systems list');

      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'deletedAt',
        sortDirection = 'DESC',
      } = queryDto;

      // Lấy danh sách agent systems đã xóa với thông tin liên quan
      const queryBuilder = this.agentSystemRepository
        .createQueryBuilder('as')
        .leftJoin('agents', 'a', 'as.id = a.id')
        .leftJoin('system_models', 'sm', 'as.systemModelId = sm.id')
        .select([
          'as.id as id',
          'as.nameCode as nameCode',
          'as.description as description',
          'as.active as active',
          'as.deletedBy as deletedBy',
          'a.name as name',
          'a.avatar as avatar',
          'a.deletedAt as deletedAt',
          'sm.modelId as modelId',
          'sm.provider as provider'
        ])
        .where('as.deletedBy IS NOT NULL')
        .andWhere('a.deletedAt IS NOT NULL');

      // Thêm search nếu có
      if (search) {
        queryBuilder.andWhere(
          '(a.name ILIKE :search OR as.nameCode ILIKE :search OR as.description ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Thêm sorting
      const sortField = sortBy === 'name' ? 'a.name' :
                       sortBy === 'nameCode' ? 'as.nameCode' :
                       'a.deletedAt'; // Default sort by deletedAt

      queryBuilder.orderBy(sortField, sortDirection as 'ASC' | 'DESC');

      // Pagination
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      const rawItems = await queryBuilder.getRawMany();
      const totalQuery = this.agentSystemRepository
        .createQueryBuilder('as')
        .leftJoin('agents', 'a', 'as.id = a.id')
        .where('as.deletedBy IS NOT NULL')
        .andWhere('a.deletedAt IS NOT NULL');

      if (search) {
        totalQuery.andWhere(
          '(a.name ILIKE :search OR as.nameCode ILIKE :search OR as.description ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      const total = await totalQuery.getCount();

      // Lấy thông tin employees đã xóa
      const deletedByIds = [...new Set(rawItems.map((item: any) => item.deletedBy).filter(Boolean))];
      const employeeInfoMap = new Map();

      if (deletedByIds.length > 0) {
        for (const employeeId of deletedByIds) {
          if (employeeId && typeof employeeId === 'number') { // Kiểm tra employeeId không null và là number
            try {
              const employeeInfo = await this.employeeInfoService.getEmployeeInfo(employeeId);
              if (employeeInfo) {
                employeeInfoMap.set(employeeId, {
                  employeeId: employeeInfo.id,
                  name: employeeInfo.name,
                  avatar: employeeInfo.avatar || null
                });
              }
            } catch (error) {
              this.logger.warn(`Failed to get employee info for ID ${employeeId}: ${error.message}`);
            }
          }
        }
      }

      // Map sang DTO
      const mappedItems: AgentSystemTrashItemDto[] = rawItems.map((item: any) => ({
        id: item.id,
        name: item.name,
        nameCode: item.nameCode,
        avatar: item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) : null,
        model: item.modelId || 'Unknown',
        provider: item.provider || null,
        active: item.active,
        deleted: item.deletedBy ? employeeInfoMap.get(item.deletedBy) : undefined
      }));

      return {
        items: mappedItems,
        meta: {
          currentPage: page,
          itemsPerPage: limit,
          totalItems: total,
          totalPages: Math.ceil(total / limit),
          itemCount: mappedItems.length
        }
      };

    } catch (error) {
      this.logger.error(`Error fetching deleted agent systems: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Khôi phục nhiều agent systems đã xóa - Optimized với bulk query
   * @param ids Danh sách ID của agent systems cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục (for logging)
   */
  @Transactional()
  async restoreAgentSystem(ids: string[], employeeId: number): Promise<void> {
    try {
      this.logger.log(`Bulk restoring agent systems: ${ids.join(', ')} by employee ${employeeId}`);

      if (!ids || ids.length === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, 'Danh sách ID không được để trống');
      }

      // 1. Tìm các agent systems đã xóa và có thể khôi phục
      const deletedAgentSystems = await this.agentSystemRepository
        .createQueryBuilder('as')
        .leftJoin('agents', 'a', 'as.id = a.id')
        .select(['as.id', 'a.id'])
        .where('as.id IN (:...ids)', { ids })
        .andWhere('as.deletedBy IS NOT NULL')
        .andWhere('a.deletedAt IS NOT NULL')
        .getRawMany();

      const validIds = deletedAgentSystems.map(item => item.as_id);

      if (validIds.length === 0) {
        this.logger.warn(`No valid deleted agent systems found for restoration from provided IDs`);
        return;
      }

      // Log các ID không hợp lệ
      const invalidIds = ids.filter(id => !validIds.includes(id));
      if (invalidIds.length > 0) {
        this.logger.warn(`Invalid or not deleted agent system IDs: ${invalidIds.join(', ')}`);
      }

      // 2. Bulk restore agents_system table - clear deletedBy
      await this.agentSystemRepository
        .createQueryBuilder()
        .update()
        .set({ deletedBy: null })
        .where('id IN (:...validIds)', { validIds })
        .andWhere('deletedBy IS NOT NULL')
        .execute();

      // 3. Bulk restore agents table - clear deletedAt
      await this.agentRepository
        .createQueryBuilder()
        .update()
        .set({ deletedAt: null })
        .where('id IN (:...validIds)', { validIds })
        .andWhere('deletedAt IS NOT NULL')
        .execute();

      this.logger.log(`Bulk restore completed. Restored: ${validIds.length}, Invalid: ${invalidIds.length}`);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error in bulk restore agent systems: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  //     // Chuyển đổi từ entity sang DTO
  //     const items = await Promise.all(
  //       result.items.map(async (agentSystem) => {
  //         const agent = agents.find(a => a.id === agentSystem.id);

  //         if (!agent) {
  //           this.logger.warn(`Agent not found for agent system ${agentSystem.id}`);
  //           return null;
  //         }

  //         const dto = new AgentSystemTrashItemDto();
  //         dto.id = agent.id;
  //         dto.name = agent.name;
  //         dto.nameCode = agentSystem.nameCode;
  //         dto.avatar = agent.avatar
  //           ? AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar)
  //           : null;
  //         dto.status = agent.status as any;

  //         // Lấy thông tin model
  //         const modelInfo = await this.createModelInfo(agentSystem);
  //         dto.model = modelInfo?.model_id || 'Unknown Model';
  //         dto.model_id = modelInfo?.model_id || null;
  //         dto.type_provider = modelInfo?.typeProvider || null;

  //         // Thông tin người xóa từ employee map
  //         const employeeInfo = employeeMap.get(agentSystem.id);
  //         if (employeeInfo) {
  //           dto.deleted = {
  //             employeeId: employeeInfo.employeeId,
  //             name: employeeInfo.employeeName,
  //             avatar: employeeInfo.employeeAvatar,
  //             date: agent.deletedAt || Date.now(),
  //           };
  //         }

  //         // Lấy thông tin vai trò (nếu có)
  //         if (agentSystem.roleId) {
  //           try {
  //             const role = await this.agentRoleRepository.findById(agentSystem.roleId);
  //             if (role) {
  //               dto.roles = {
  //                 id: role.id,
  //                 name: role.name,
  //               };
  //             }
  //           } catch (error) {
  //             this.logger.warn(`Cannot get role info for agent system ${agentSystem.id}: ${error.message}`);
  //           }
  //         }

  //         return dto;
  //       })
  //     );

  //     // Filter out null items
  //     const validItems = items.filter(item => item !== null) as AgentSystemTrashItemDto[];

  //     return {
  //       items: validItems,
  //       meta: {
  //         totalItems: result.total,
  //         itemCount: validItems.length,
  //         itemsPerPage: limit,
  //         totalPages: Math.ceil(result.total / limit),
  //         currentPage: page,
  //       },
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error finding deleted agent systems: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }
  // }

  // /**
  //  * Khôi phục agent system đã xóa
  //  * @param id ID của agent system cần khôi phục
  //  * @param employeeId ID của nhân viên thực hiện khôi phục
  //  */
  // @Transactional()
  // async restoreAgentSystem(id: string, employeeId: number): Promise<void> {
  //   try {
  //     // Kiểm tra agent system đã xóa có tồn tại không
  //     const deletedAgentSystem = await this.agentSystemRepository.findOne({
  //       where: { id },
  //       withDeleted: true,
  //     });

  //     if (!deletedAgentSystem || !deletedAgentSystem.deletedBy) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, 'Agent system đã xóa không tồn tại');
  //     }

  //     // Kiểm tra agent đã xóa có tồn tại không
  //     const deletedAgent = await this.agentRepository.findOne({
  //       where: { id },
  //       withDeleted: true,
  //     });

  //     if (!deletedAgent || !deletedAgent.deletedAt) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Agent đã xóa không tồn tại');
  //     }

  //     // Khôi phục agent system
  //     const agentSystemRestored = await this.agentSystemRepository.restoreAgentSystem(id, employeeId);
  //     if (!agentSystemRestored) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, 'Không thể khôi phục agent system');
  //     }

  //     // Khôi phục agent
  //     const agentResult = await this.agentRepository.restore(id);
  //     if (!agentResult.affected || agentResult.affected === 0) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Không thể khôi phục agent');
  //     }

  //     this.logger.debug(`Đã khôi phục agent system với ID ${id}`);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error restoring agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  // /**
  //  * Xóa vai trò khỏi agent system
  //  * @param agentId ID của agent system
  //  * @param roleId ID của vai trò
  //  */
  // @Transactional()
  // async removeRoleFromAgentSystem(
  //   agentId: string,
  //   roleId: string,
  // ): Promise<void> {
  //   // Kiểm tra agent system có tồn tại không
  //   const agentSystem = await this.agentSystemRepository.findById(agentId);
  //   if (!agentSystem) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  //   }

  //   // Kiểm tra vai trò có tồn tại không
  //   const role = await this.agentRoleRepository.findById(roleId);
  //   if (!role) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
  //   }

  //   try {

  //     // Lưu vai trò
  //     await this.agentRoleRepository.save(role);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error removing role from agent system: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  //   }
  // }

  // /**
  //  * Kiểm tra model có tồn tại không và có trạng thái APPROVED không
  //  * @param modelId ID của model
  //  */
  // private async validateModel(
  //   modelId: string,
  // ): Promise<void> {
  //   try {
  //     // Kiểm tra model có tồn tại không bằng cách gọi service của module Model
  //     const modelResponse = await this.adminModelBaseService.findOne(modelId);
  //     const model = modelResponse.result;

  //     if (!model) {
  //       throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //     }

  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating model: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //   }
  // }

  // /**
  //  * Kiểm tra vector store có tồn tại không
  //  * @param vectorStoreId ID của vector store
  //  */
  // private async validateVectorStore(vectorStoreId: string): Promise<void> {
  //   try {
  //     // Trong thực tế, bạn sẽ gọi service của module Vector Store để kiểm tra
  //     // Ở đây, chúng ta sẽ giả định vector store tồn tại
  //     const vectorStoreExists = true;

  //     if (!vectorStoreExists) {
  //       throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND);
  //     }
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating vector store: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND);
  //   }
  // }

  // /**
  //  * Kiểm tra base model có tồn tại không và có trạng thái APPROVED không
  //  * @param modelBaseId ID của base model
  //  */
  // private async validateBaseModel(modelBaseId: string): Promise<void> {
  //   try {
  //     // Kiểm tra base model có tồn tại không bằng cách gọi service của module Model
  //     const baseModelResponse = await this.adminModelBaseService.findOne(modelBaseId);
  //     const baseModel = baseModelResponse.result;

  //     if (!baseModel) {
  //       throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //     }

  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating base model: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //   }
  // }

  // /**
  //  * Kiểm tra fine-tuning model có tồn tại không và có trạng thái APPROVED không
  //  * @param modelFinetuningId ID của fine-tuning model
  //  */
  // private async validateFineTuningModel(modelFinetuningId: string): Promise<void> {
  //   try {
  //     // TODO: Implement fine-tuning model validation when service is available
  //     // For now, just log the validation attempt
  //     this.logger.debug(`Fine-tuning model validation for ID: ${modelFinetuningId} - skipped (service not implemented)`);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating fine-tuning model: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
  //   }
  // }

  // /**
  //  * Kiểm tra vai trò có tồn tại không
  //  * @param roleId ID của vai trò
  //  */
  // private async validateRole(roleId: string): Promise<void> {
  //   const role = await this.agentRoleRepository.findById(roleId);
  //   if (!role) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
  //   }
  // }

  // /**
  //  * Lấy thông tin vector store
  //  * @param agent Thông tin agent
  //  * @returns Thông tin vector store hoặc null nếu không có
  //  */
  // private async getVectorStoreInfo(
  //   agent: Agent,
  // ): Promise<VectorStoreDto | null> {
  //   try {
  //     // Kiểm tra xem agent có vectorStoreId không
  //     if (!agent.vectorStoreId) {
  //       return null;
  //     }

  //     // Trong thực tế, bạn sẽ gọi service của module Vector Store để lấy thông tin
  //     // Ở đây, chúng ta sẽ kiểm tra xem vectorStoreId có hợp lệ không
  //     // Nếu vectorStoreId là giá trị mặc định hoặc không hợp lệ, trả về null
  //     if (agent.vectorStoreId === 'vector-store-1' || agent.vectorStoreId === 'default') {
  //       return null;
  //     }

  //     // Trả về thông tin vector store thực sự
  //     return {
  //       vectorStoreId: agent.vectorStoreId,
  //       vectorStoreName: `Vector Store ${agent.vectorStoreId}`,
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error getting vector store info: ${error.message}`,
  //       error.stack,
  //     );
  //     return null;
  //   }
  // }

  // /**
  //  * Resolve agent model name từ agent base fields
  //  * @param agentId Agent ID để lấy thông tin từ agent base
  //  * @returns Model name
  //  */
  // private async resolveAgentModelName(agentId: string): Promise<string> {
  //   try {
  //     // Lấy thông tin agent base để có model fields
  //     const agentBase = await this.agentBaseRepository.findById(agentId);
  //     if (!agentBase) {
  //       return 'Unknown';
  //     }

  //     if (agentBase.modelBaseId) {
  //       // TODO: Join với base_models table để lấy model name
  //       return `base-model-${agentBase.modelBaseId}`;
  //     }

  //     if (agentBase.modelFinetuningId) {
  //       // TODO: Join với finetuning_models table để lấy model name
  //       return `finetuning-model-${agentBase.modelFinetuningId}`;
  //     }

  //     return 'Unknown';
  //   } catch (error) {
  //     this.logger.error(`Error resolving agent model name: ${error.message}`);
  //     return 'Unknown';
  //   }
  // }

  // /**
  //  * Lấy thông tin nhà cung cấp model
  //  * @param providerId ID của nhà cung cấp (UUID)
  //  * @returns Tên nhà cung cấp
  //  */
  // private async getProviderInfo(providerId: string): Promise<string> {
  //   try {
  //     // Trong thực tế, bạn sẽ gọi service của module Model để lấy thông tin
  //     // Ở đây, chúng ta sẽ tạo một mapping giả định
  //     const providerMap: Record<string, string> = {
  //       '123e4567-e89b-12d3-a456-************': 'Anthropic',
  //       '123e4567-e89b-12d3-a456-************': 'OpenAI',
  //       '123e4567-e89b-12d3-a456-************': 'Google',
  //       '123e4567-e89b-12d3-a456-************': 'Meta AI',
  //     };

  //     return providerMap[providerId] || 'Unknown Provider';
  //   } catch (error) {
  //     this.logger.error(
  //       `Error getting provider info: ${error.message}`,
  //       error.stack,
  //     );
  //     return 'Unknown Provider';
  //   }
  // }

  // /**
  //  * Tạo thông tin model từ model_base_id hoặc model_finetuning_id
  //  * @param agentSystem Entity AgentSystem
  //  * @returns Thông tin model hoặc undefined nếu không có
  //  */
  // private async createModelInfo(agentSystem: AgentSystem): Promise<{ model_id: string; typeProvider: string } | undefined> {
  //   try {
  //     // Nếu có model_base_id, lấy thông tin từ base model
  //     if (agentSystem.modelBaseId) {
  //       try {
  //         const baseModelResponse = await this.adminModelBaseService.findOne(agentSystem.modelBaseId);
  //         const baseModel = baseModelResponse.result;
  //         if (baseModel && baseModel.modelId && baseModel.provider) {
  //           return {
  //             model_id: baseModel.modelId,
  //             typeProvider: baseModel.provider as string,
  //           };
  //         }
  //       } catch (error) {
  //         this.logger.warn(`Không thể lấy thông tin base model ${agentSystem.modelBaseId}: ${error.message}`);
  //       }
  //     }

  //     // TODO: Xử lý model_finetuning_id khi có service
  //     if (agentSystem.modelFinetuningId) {
  //       this.logger.debug(`Finetuning model service chưa được implement: ${agentSystem.modelFinetuningId}`);
  //     }

  //     return undefined;
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi tạo thông tin model: ${error.message}`, error.stack);
  //     return undefined;
  //   }
  // }

  // /**
  //  * Lấy thông tin nhân viên
  //  * @param employeeId ID của nhân viên
  //  * @param date Thời gian tạo/cập nhật/xóa
  //  * @returns Thông tin nhân viên
  //  */
  // private async getEmployeeInfo(employeeId: number, date?: number): Promise<EmployeeInfoSimpleDto> {
  //   const employeeInfo = await this.employeeInfoService.getEmployeeInfo(employeeId);

  //   // Thêm trường date nếu có, sử dụng type assertion để tránh lỗi TypeScript
  //   if (date) {
  //     (employeeInfo as any).date = date;
  //   }

  //   return employeeInfo;
  // }

  // /**
  //  * Chuyển đổi Agent thành AgentSystemListItemDto
  //  * @param agent Entity Agent
  //  * @param roles Danh sách vai trò
  //  * @returns AgentSystemListItemDto
  //  */
  // private async mapToListItemDto(
  //   agent: Agent,
  //   roles: SimpleRoleDto | null,
  // ): Promise<AgentSystemListItemDto> {
  //   try {
  //     // Lấy thông tin agent system
  //     const agentSystem = await this.agentSystemRepository.findById(agent.id);

  //     if (!agentSystem) {
  //       this.logger.warn(`Agent system not found for agent ID: ${agent.id}`);
  //       // Trả về thông tin cơ bản nếu không tìm thấy agent system
  //       // Tạo response DTO
  //       const response: any = {
  //         id: agent.id,
  //         name: agent.name,
  //         nameCode: 'unknown',
  //         avatar: agent.avatar
  //           ? AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar)
  //           : null,
  //         model: await this.resolveAgentModelName(agent.id),
  //         status: agent.status,
  //       };

  //       // Chỉ thêm trường roles nếu có vai trò thực sự
  //       if (roles && roles.id) {
  //         response.roles = roles;
  //       }

  //       return response;
  //     }

  //     // Tạo URL avatar an toàn
  //     let avatarUrl: string | null = null;
  //     if (agent.avatar) {
  //       try {
  //         avatarUrl = AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar);
  //       } catch (avatarError) {
  //         this.logger.error(`Error generating avatar URL: ${avatarError.message}`, avatarError.stack);
  //       }
  //     }

  //     // Lấy thông tin model
  //     const modelInfo = await this.createModelInfo(agentSystem);

  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode,
  //       avatar: avatarUrl,
  //       model: modelInfo?.model_id || 'Unknown Model',
  //       model_id: modelInfo?.model_id || null,
  //       type_provider: modelInfo?.typeProvider || null,
  //       status: agent.status,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   } catch (error) {
  //     this.logger.error(`Error mapping agent to list item DTO: ${error.message}`, error.stack);
  //     // Trả về thông tin cơ bản nếu có lỗi
  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: 'error',
  //       avatar: null,
  //       model: 'Unknown',
  //       status: agent.status,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   }
  // }

  // /**
  //  * Lấy thông tin agent system với tất cả dữ liệu liên quan trong một query tối ưu
  //  * @param id ID của agent system
  //  * @returns Thông tin chi tiết agent system với các dữ liệu liên quan
  //  */
  // private async getAgentSystemWithAllDetails(id: string): Promise<{
  //   agent: Agent;
  //   agentSystem: AgentSystem;
  //   role?: AgentRole;
  //   createdByEmployee?: { id: number; fullName: string; avatar?: string };
  //   updatedByEmployee?: { id: number; fullName: string; avatar?: string };
  //   deletedByEmployee?: { id: number; fullName: string; avatar?: string };
  // } | null> {
  //   try {
  //     // Lấy thông tin agent
  //     const agent = await this.agentRepository.findById(id);
  //     if (!agent) {
  //       return null;
  //     }

  //     // Lấy thông tin agent system
  //     const agentSystem = await this.agentSystemRepository.findById(id);
  //     if (!agentSystem) {
  //       return null;
  //     }

  //     // Lấy thông tin role
  //     let role: AgentRole | undefined;
  //     if (agentSystem.roleId) {
  //       const foundRole = await this.agentRoleRepository.findById(agentSystem.roleId);
  //       role = foundRole || undefined;
  //     }

  //     // Lấy thông tin employees trong một batch để tối ưu
  //     const employeeIds = [
  //       agentSystem.createdBy,
  //       agentSystem.updatedBy,
  //       agentSystem.deletedBy,
  //     ].filter((id): id is number => id !== null && id !== undefined);

  //     const employeeInfoMap = new Map<number, { id: number; fullName: string; avatar?: string }>();

  //     if (employeeIds.length > 0) {
  //       // Batch query employees để giảm số lượng queries
  //       for (const employeeId of employeeIds) {
  //         try {
  //           const employeeInfo = await this.employeeInfoService.getEmployeeInfo(employeeId);
  //           if (employeeInfo) {
  //             employeeInfoMap.set(employeeId, {
  //               id: employeeId,
  //               fullName: employeeInfo.name,
  //               avatar: employeeInfo.avatar || undefined,
  //             });
  //           }
  //         } catch (error) {
  //           this.logger.warn(`Không thể lấy thông tin employee ${employeeId}: ${error.message}`);
  //         }
  //       }
  //     }

  //     return {
  //       agent,
  //       agentSystem,
  //       role,
  //       createdByEmployee: agentSystem.createdBy ? employeeInfoMap.get(agentSystem.createdBy) : undefined,
  //       updatedByEmployee: agentSystem.updatedBy ? employeeInfoMap.get(agentSystem.updatedBy) : undefined,
  //       deletedByEmployee: agentSystem.deletedBy ? employeeInfoMap.get(agentSystem.deletedBy) : undefined,
  //     };
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi lấy chi tiết agent system ${id}: ${error.message}`, error.stack);
  //     return null;
  //   }
  // }

  // /**
  //  * Chuyển đổi Agent và AgentSystem thành AgentSystemDetailDto (optimized version)
  //  * @param agent Entity Agent
  //  * @param agentSystem Entity AgentSystem
  //  * @param vectorStore Thông tin vector store hoặc null
  //  * @param roles Danh sách vai trò
  //  * @param createdByEmployee Thông tin nhân viên tạo
  //  * @param updatedByEmployee Thông tin nhân viên cập nhật
  //  * @param deletedByEmployee Thông tin nhân viên xóa
  //  * @returns AgentSystemDetailDto
  //  */
  // private async mapToDetailDtoOptimized(
  //   agent: Agent,
  //   agentSystem: AgentSystem,
  //   vectorStore: VectorStoreDto | null,
  //   roles: SimpleRoleDto | null,
  //   createdByEmployee?: { id: number; fullName: string; avatar?: string },
  //   updatedByEmployee?: { id: number; fullName: string; avatar?: string },
  //   deletedByEmployee?: { id: number; fullName: string; avatar?: string },
  // ): Promise<AgentSystemDetailDto> {
  //   try {
  //     // Tạo model config response - chỉ bao gồm 4 trường cần thiết
  //     const modelConfig: ModelConfigResponseDto = {
  //       temperature: agent.modelConfig?.temperature,
  //       top_p: agent.modelConfig?.top_p,
  //       top_k: agent.modelConfig?.top_k,
  //       max_tokens: agent.modelConfig?.max_tokens,
  //     };

  //     // Tạo URL avatar an toàn
  //     let avatarUrl: string | null = null;
  //     if (agent.avatar) {
  //       try {
  //         avatarUrl = AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar);
  //       } catch (avatarError) {
  //         this.logger.error(`Error generating avatar URL: ${avatarError.message}`, avatarError.stack);
  //       }
  //     }

  //     // Lấy thông tin model
  //     const modelInfo = await this.createModelInfo(agentSystem);

  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode,
  //       avatar: avatarUrl,
  //       modelConfig,
  //       instruction: agent.instruction,
  //       vector: vectorStore,
  //       status: agent.status,
  //       model_id: modelInfo?.model_id || null,
  //       type_provider: modelInfo?.typeProvider || null,
  //     };

  //     // Thêm thông tin nhân viên từ optimized query
  //     if (createdByEmployee) {
  //       response.created = {
  //         employeeId: createdByEmployee.id,
  //         name: createdByEmployee.fullName,
  //         avatar: createdByEmployee.avatar,
  //         date: agent.createdAt,
  //       };
  //     }

  //     if (updatedByEmployee) {
  //       response.updated = {
  //         employeeId: updatedByEmployee.id,
  //         name: updatedByEmployee.fullName,
  //         avatar: updatedByEmployee.avatar,
  //         date: agent.updatedAt,
  //       };
  //     }

  //     if (deletedByEmployee) {
  //       response.deleted = {
  //         employeeId: deletedByEmployee.id,
  //         name: deletedByEmployee.fullName,
  //         avatar: deletedByEmployee.avatar,
  //         date: agent.deletedAt || undefined,
  //       };
  //     }

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   } catch (error) {
  //     this.logger.error(`Error mapping agent to detail DTO: ${error.message}`, error.stack);
  //     // Trả về thông tin cơ bản nếu có lỗi
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode || 'error',
  //       avatar: null,
  //       modelConfig: {
  //         temperature: undefined,
  //         top_p: undefined,
  //         top_k: undefined,
  //         max_tokens: undefined,
  //       } as ModelConfigResponseDto,
  //       instruction: agent.instruction,
  //       vector: null,
  //       status: agent.status,
  //       created: undefined,
  //       updated: undefined,
  //       deleted: undefined,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   }
  // }

  // /**
  //  * Chuyển đổi Agent và AgentSystem thành AgentSystemDetailDto
  //  * @param agent Entity Agent
  //  * @param agentSystem Entity AgentSystem
  //  * @param vectorStore Thông tin vector store hoặc null
  //  * @param roles Danh sách vai trò
  //  * @returns AgentSystemDetailDto
  //  */
  // private async mapToDetailDto(
  //   agent: Agent,
  //   agentSystem: AgentSystem,
  //   vectorStore: VectorStoreDto | null,
  //   roles: SimpleRoleDto | null,
  // ): Promise<AgentSystemDetailDto> {
  //   // Lấy thông tin nhân viên tạo, cập nhật, xóa
  //   let created: EmployeeInfoDto | undefined, updated: EmployeeInfoDto | undefined, deleted: EmployeeInfoDto | undefined;

  //   try {

  //     // Tạo model config response - chỉ bao gồm 4 trường cần thiết
  //     const modelConfig: ModelConfigResponseDto = {
  //       temperature: agent.modelConfig?.temperature,
  //       top_p: agent.modelConfig?.top_p,
  //       top_k: agent.modelConfig?.top_k,
  //       max_tokens: agent.modelConfig?.max_tokens,
  //     };

  //     // Tạo URL avatar an toàn
  //     let avatarUrl: string | null = null;
  //     if (agent.avatar) {
  //       try {
  //         avatarUrl = AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar);
  //       } catch (avatarError) {
  //         this.logger.error(`Error generating avatar URL: ${avatarError.message}`, avatarError.stack);
  //       }
  //     }

  //     // Lấy thông tin model
  //     const modelInfo = await this.createModelInfo(agentSystem);

  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode,
  //       avatar: avatarUrl,
  //       modelConfig,
  //       instruction: agent.instruction,
  //       vector: vectorStore,
  //       status: agent.status,
  //       model_id: modelInfo?.model_id || null,
  //       type_provider: modelInfo?.typeProvider || null,
  //       created,
  //       updated,
  //       deleted,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   } catch (error) {
  //     this.logger.error(`Error mapping agent to detail DTO: ${error.message}`, error.stack);
  //     // Trả về thông tin cơ bản nếu có lỗi
  //     // Tạo response DTO
  //     const response: any = {
  //       id: agent.id,
  //       name: agent.name,
  //       nameCode: agentSystem.nameCode || 'error',
  //       avatar: null,
  //       modelConfig: {
  //         temperature: undefined,
  //         top_p: undefined,
  //         top_k: undefined,
  //         max_tokens: undefined,
  //       } as ModelConfigResponseDto,
  //       instruction: agent.instruction,
  //       vector: null,
  //       status: agent.status,
  //       created: undefined,
  //       updated: undefined,
  //       deleted: undefined,
  //     };

  //     // Chỉ thêm trường roles nếu có vai trò thực sự
  //     if (roles && roles.id) {
  //       response.roles = roles;
  //     }

  //     return response;
  //   }
  // }

  /**
   * Tạo agent system mới
   * @param createDto Dữ liệu tạo agent system
   * @param employeeId ID của nhân viên tạo
   * @returns ID của agent system đã tạo và URL tải lên avatar (nếu có)
   */
  async create(
    createDto: CreateAgentSystemDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Creating agent system with nameCode: ${createDto.nameCode}`);

      // 1. Validate system model exists
      await this.validateSystemModel(createDto.modelId);

      // 2. Validate unique nameCode
      await this.validateUniqueNameCode(createDto.nameCode);

      // 3. Validate unique supervisor (nếu isSupervisor = true)
      if (createDto.isSupervisor) {
        await this.validateUniqueSupervisor();
      }

      // 4. Validate MCP systems exist (nếu có)
      if (createDto.mcpId && createDto.mcpId.length > 0) {
        await this.validateMcpSystems(createDto.mcpId);
      }

      // 5. Tạo agent record trước
      const agent = await this.agentRepository.create({
        name: createDto.name,
        modelConfig: createDto.modelConfig || {},
        instruction: createDto.instruction || '',
        avatar: null, // Sẽ được cập nhật sau khi upload
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // 8. Tạo avatar upload URL (nếu có)
      let avatarUrlUpload: string | undefined;
      if (createDto.avatarMimeType) {
        
        const key = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
        });

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          key,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(createDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );

        agent.avatar = key;
      }

      const agentSave = await this.agentRepository.save(agent);

     const agentSystem = await this.agentSystemRepository.create({
        id: agentSave.id,
        nameCode: createDto.nameCode,
        description: createDto.description || undefined,
        isSupervisor: createDto.isSupervisor || false,
        active: true, // Default active
        systemModelId: createDto.modelId,
        createdBy: employeeId,
        updatedBy: employeeId,
      });

      await this.agentSystemRepository.save(agentSystem);

      // 7. Liên kết với MCP systems (nếu có)
      if (createDto.mcpId && createDto.mcpId.length > 0) {
        await this.linkAgentWithMcpSystems(agentSave.id, createDto.mcpId);
      }

      this.logger.log(`Successfully created agent system with ID: ${agentSave.id}`);

      return {
        id: agentSave.id,
        avatarUrlUpload,
      };

    } catch (error) {
      this.logger.error(`Failed to create agent system: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Không thể tạo agent system: ${error.message}`
      );
    }
  }

  /**
   * Validate system model exists
   * @param modelId ID của system model
   */
  private async validateSystemModel(modelId: string): Promise<void> {
    const systemModel = await this.systemModelsRepository.findOne({
      where: { id: modelId }
    });

    if (!systemModel) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
    }
  }

  /**
   * Validate unique nameCode
   * @param nameCode Name code để kiểm tra
   */
  private async validateUniqueNameCode(nameCode: string): Promise<void> {
    const existingAgentSystem = await this.agentSystemRepository.findOne({
      where: { nameCode, deletedBy: IsNull() }
    });

    if (existingAgentSystem) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
    }
  }

  /**
   * Validate unique supervisor
   */
  private async validateUniqueSupervisor(): Promise<void> {
    const existingSupervisor = await this.agentSystemRepository.findOne({
      where: { isSupervisor: true, deletedBy: IsNull() }
    });

    if (existingSupervisor) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS, // Sử dụng error code có sẵn
        'Chỉ được có một agent system supervisor'
      );
    }
  }

  /**
   * Validate MCP systems exist
   * @param mcpIds Danh sách MCP system IDs
   */
  private async validateMcpSystems(mcpIds: string[]): Promise<void> {
    for (const mcpId of mcpIds) {
      const mcpSystem = await this.mcpSystemsRepository.findOne({
        where: { id: mcpId }
      });

      if (!mcpSystem) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, // Sử dụng error code có sẵn
          `MCP System với ID ${mcpId} không tồn tại`
        );
      }
    }
  }

  /**
   * Validate agent system exists by ID
   * @param id ID của agent system
   * @returns Agent system entity
   */
  private async validateAgentSystemExists(id: string): Promise<{ agent: Agent; agentSystem: AgentSystem }> {
    // Kiểm tra agent có tồn tại không
    const agent = await this.agentRepository.findOne({
      where: { id, deletedAt: IsNull() }
    });

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Kiểm tra agent system có tồn tại không
    const agentSystem = await this.agentSystemRepository.findOne({
      where: { id, deletedBy: IsNull() }
    });

    if (!agentSystem) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
    }

    return { agent, agentSystem };
  }

  /**
   * Validate unique nameCode for update (exclude current agent system)
   * @param nameCode Name code để kiểm tra
   * @param currentId ID của agent system hiện tại (để loại trừ)
   */
  private async validateUniqueNameCodeForUpdate(nameCode: string, currentId: string): Promise<void> {
    const existingAgentSystem = await this.agentSystemRepository.findOne({
      where: { nameCode, deletedBy: IsNull() }
    });

    if (existingAgentSystem && existingAgentSystem.id !== currentId) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
    }
  }

  /**
   * Validate unique supervisor for update (exclude current agent system)
   * @param isSupervisor Có phải supervisor không
   * @param currentId ID của agent system hiện tại (để loại trừ)
   */
  private async validateUniqueSupervisorForUpdate(isSupervisor: boolean, currentId: string): Promise<void> {
    if (!isSupervisor) return; // Không cần validate nếu không phải supervisor

    const existingSupervisor = await this.agentSystemRepository.findOne({
      where: { isSupervisor: true, deletedBy: IsNull() }
    });

    if (existingSupervisor && existingSupervisor.id !== currentId) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS, // Sử dụng error code có sẵn
        'Chỉ được có một agent system supervisor'
      );
    }
  }

  /**
   * Validate vector store exists (simplified validation)
   * @param vectorStoreId ID của vector store
   */
  private async validateVectorStore(vectorStoreId: string): Promise<void> {
    // TODO: Implement proper vector store validation when service is available
    // For now, just validate the format
    if (!vectorStoreId || vectorStoreId.trim().length === 0) {
      throw new AppException(
        AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
        'Vector store ID không hợp lệ'
      );
    }

    this.logger.debug(`Vector store validation for ID: ${vectorStoreId} - basic format check passed`);
  }

  /**
   * Lấy thông tin system model
   * @param systemModelId ID của system model
   * @returns SystemModelDto
   */
  private async getSystemModelInfo(systemModelId: string): Promise<SystemModelDto> {
    const systemModel = await this.systemModelsRepository
      .createQueryBuilder('sm')
      .leftJoin('model_registry', 'mr', 'sm.modelId = mr.modelId AND sm.provider = mr.provider')
      .select([
        'sm.id',
        'sm.modelId',
        'sm.provider',
        'sm.active',
        'mr.modelNamePattern',
        'mr.inputModalities',
        'mr.outputModalities',
        'mr.samplingParameters',
        'mr.features',
        'mr.basePricing',
        'mr.fineTunePricing',
        'mr.trainingPricing'
      ])
      .where('sm.id = :systemModelId', { systemModelId })
      .getRawOne();

    if (!systemModel) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
    }

    return {
      id: systemModel.sm_id,
      modelId: systemModel.sm_modelId,
      provider: systemModel.sm_provider,
      modelNamePattern: systemModel.mr_modelNamePattern || systemModel.sm_modelId,
      active: systemModel.sm_active,
      inputModalities: systemModel.mr_inputModalities,
      outputModalities: systemModel.mr_outputModalities,
      samplingParameters: systemModel.mr_samplingParameters,
      features: systemModel.mr_features,
      basePricing: systemModel.mr_basePricing,
      fineTunePricing: systemModel.mr_fineTunePricing,
      trainingPricing: systemModel.mr_trainingPricing
    };
  }

  /**
   * Lấy danh sách MCP systems của agent
   * @param agentId ID của agent
   * @returns Danh sách McpSystemDto
   */
  private async getMcpSystemsInfo(agentId: string): Promise<McpSystemDto[]> {
    const mcpSystems = await this.agentSystemMcpRepository
      .createQueryBuilder('asm')
      .leftJoin('mcp_systems', 'mcp', 'asm.mcpId = mcp.id')
      .select([
        'mcp.id',
        'mcp.nameServer',
        'mcp.description',
        'mcp.config',
        'mcp.active',
        'mcp.createdAt',
        'mcp.updatedAt'
      ])
      .where('asm.agentId = :agentId', { agentId })
      .getRawMany();

    return mcpSystems.map(mcp => ({
      id: mcp.mcp_id,
      nameServer: mcp.mcp_nameServer,
      description: mcp.mcp_description,
      config: mcp.mcp_config,
      active: mcp.mcp_active,
      createdAt: mcp.mcp_createdAt,
      updatedAt: mcp.mcp_updatedAt
    }));
  }

  /**
   * Lấy thông tin vector store (simplified)
   * @param vectorStoreId ID của vector store
   * @returns Vector store info hoặc null
   */
  private async getVectorStoreInfo(vectorStoreId?: string | null): Promise<{ vectorStoreId: string; vectorStoreName: string } | null> {
    if (!vectorStoreId) {
      return null;
    }

    // TODO: Implement proper vector store service when available
    // For now, return basic info
    return {
      vectorStoreId,
      vectorStoreName: `Vector Store ${vectorStoreId.substring(0, 8)}`
    };
  }

  /**
   * Lấy thông tin employees (created, updated)
   * @param agentSystem Agent system entity
   * @returns Employee info
   */
  private async getEmployeeInfo(agentSystem: any): Promise<{
    created: { id: number; fullName: string; avatar?: string } | null;
    updated: { id: number; fullName: string; avatar?: string } | null;
  }> {
    const result = {
      created: null as { id: number; fullName: string; avatar?: string } | null,
      updated: null as { id: number; fullName: string; avatar?: string } | null
    };

    // Lấy thông tin employee created
    if (agentSystem.createdBy) {
      try {
        const createdEmployee = await this.employeeInfoService.getEmployeeInfo(agentSystem.createdBy);
        if (createdEmployee) {
          result.created = {
            id: createdEmployee.id,
            fullName: createdEmployee.name, // EmployeeInfoSimpleDto có trường 'name'
            avatar: createdEmployee.avatar || undefined
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to get created employee info: ${error.message}`);
      }
    }

    // Lấy thông tin employee updated
    if (agentSystem.updatedBy && agentSystem.updatedBy !== agentSystem.createdBy) {
      try {
        const updatedEmployee = await this.employeeInfoService.getEmployeeInfo(agentSystem.updatedBy);
        if (updatedEmployee) {
          result.updated = {
            id: updatedEmployee.id,
            fullName: updatedEmployee.name, // EmployeeInfoSimpleDto có trường 'name'
            avatar: updatedEmployee.avatar || undefined
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to get updated employee info: ${error.message}`);
      }
    } else if (agentSystem.updatedBy === agentSystem.createdBy) {
      // Nếu cùng một người thì copy thông tin
      result.updated = result.created;
    }

    return result;
  }

  /**
   * Map entities sang AgentSystemDetailDto
   * @param agent Agent entity
   * @param agentSystem AgentSystem entity
   * @param systemModel SystemModelDto
   * @param mcpSystems McpSystemDto[]
   * @param vectorStore Vector store info
   * @param employeeInfo Employee info
   * @returns AgentSystemDetailDto
   */
  private async mapToDetailDto(
    agent: any,
    agentSystem: any,
    systemModel: SystemModelDto,
    mcpSystems: McpSystemDto[],
    vectorStore: { vectorStoreId: string; vectorStoreName: string } | null,
    employeeInfo: {
      created: { id: number; fullName: string; avatar?: string } | null;
      updated: { id: number; fullName: string; avatar?: string } | null;
    }
  ): Promise<AgentSystemDetailDto> {
    // Generate avatar URL nếu có
    const avatarUrl = agent.avatar
      ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY)
      : null;

    // Map vector store
    const vectorDto = vectorStore ? {
      vectorStoreId: vectorStore.vectorStoreId,
      vectorStoreName: vectorStore.vectorStoreName
    } : undefined;

    // Map employee info
    const createdDto = employeeInfo.created ? {
      employeeId: employeeInfo.created.id,
      name: employeeInfo.created.fullName,
      avatar: employeeInfo.created.avatar || null
    } : undefined;

    const updatedDto = employeeInfo.updated ? {
      employeeId: employeeInfo.updated.id,
      name: employeeInfo.updated.fullName,
      avatar: employeeInfo.updated.avatar || null
    } : undefined;

    return {
      id: agentSystem.id,
      name: agent.name,
      nameCode: agentSystem.nameCode,
      avatar: avatarUrl,
      modelConfig: agent.modelConfig,
      instruction: agent.instruction,
      description: agentSystem.description,
      vector: vectorDto,
      model: systemModel,
      mcp: mcpSystems.length > 0 ? mcpSystems : undefined,
      created: createdDto,
      updated: updatedDto
    };
  }

  /**
   * Tạo agent record
   * @param createDto Dữ liệu tạo
   * @returns Agent ID
   */
  private async createAgentRecord(createDto: CreateAgentSystemDto): Promise<string> {
    const agentData = {
      name: createDto.name,
      modelConfig: createDto.modelConfig || {},
      instruction: createDto.instruction || '',
      avatar: null, // Sẽ được cập nhật sau khi upload
    };

    const savedAgent = await this.agentRepository.save(agentData);
    return savedAgent.id;
  }

  /**
   * Tạo agent system record
   * @param agentId Agent ID
   * @param createDto Dữ liệu tạo
   * @param employeeId Employee ID
   */
  private async createAgentSystemRecord(
    agentId: string,
    createDto: CreateAgentSystemDto,
    employeeId: number,
  ): Promise<void> {
    const agentSystemData = {
      id: agentId,
      nameCode: createDto.nameCode,
      description: createDto.description || undefined,
      isSupervisor: createDto.isSupervisor || false,
      active: true, // Default active
      systemModelId: createDto.modelId,
      createdBy: employeeId,
      updatedBy: employeeId,
    };

    await this.agentSystemRepository.save(agentSystemData);
  }

  /**
   * Liên kết agent với MCP systems
   * @param agentId Agent ID
   * @param mcpIds Danh sách MCP system IDs
   */
  private async linkAgentWithMcpSystems(agentId: string, mcpIds: string[]): Promise<void> {
    await this.agentSystemMcpRepository.linkAgentWithMcps(agentId, mcpIds);
  }

  /**
   * Tạo avatar upload URL
   * @param agentId Agent ID
   * @param mimeType MIME type của avatar
   * @returns Upload URL
   */
  private async generateAvatarUploadUrl(agentId: string, mimeType: string): Promise<string> {
    try {
      // Validate MIME type (basic validation)
      const validMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validMimeTypes.includes(mimeType)) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'MIME type không hợp lệ cho avatar'
        );
      }

      // Generate simple S3 key
      const extension = mimeType.split('/')[1];
      const s3Key = `agents/${agentId}/avatar.${extension}`;

      // Generate upload URL (simplified)
      const uploadUrl = `https://s3.amazonaws.com/upload/${s3Key}?agentId=${agentId}&mimeType=${mimeType}`;

      return uploadUrl;
    } catch (error) {
      this.logger.error(`Failed to generate avatar upload URL: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể tạo URL upload avatar'
      );
    }
  }
}
