import { ProviderEnum } from '@modules/models/constants';
import {
  FeatureEnum,
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum
} from '@modules/models/constants/model-capabilities.enum';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin system model trong agent system response
 */
export class SystemModelDto {
  /**
   * UUID của system model
   */
  @ApiProperty({
    description: 'UUID của system model',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * ID định danh của model
   */
  @ApiProperty({
    description: 'ID định danh của model',
    example: 'gpt-4o',
  })
  modelId: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  provider: ProviderEnum;

  /**
   * <PERSON><PERSON><PERSON> phương thức input được hỗ trợ
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> phương thức input được hỗ trợ',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
  })
  inputModalities?: InputModalityEnum[];

  /**
   * Các phương thức output được hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các phương thức output được hỗ trợ',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  outputModalities?: OutputModalityEnum[];

  /**
   * Các tham số sampling được hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling được hỗ trợ',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  samplingParameters?: SamplingParameterEnum[];

  /**
   * Các tính năng được hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các tính năng được hỗ trợ',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL, FeatureEnum.TOOL_CALL],
  })
  features?: FeatureEnum[];
}
